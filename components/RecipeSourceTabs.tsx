import React from 'react';
import { View } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';
import { RecipeSource } from '@/components/types';
import { RECIPE_SOURCES } from '@/constants/RecipeConstants';

interface RecipeSourceTabsProps {
  selectedSource: RecipeSource;
  onSourceChange: (source: RecipeSource) => void;
}

const RecipeSourceTabs: React.FC<RecipeSourceTabsProps> = ({
  selectedSource,
  onSourceChange,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');

  return (
    <View style={{ flexDirection: 'row', paddingHorizontal: 16, paddingVertical: 8 }}>
      {RECIPE_SOURCES.map((source) => (
        <Card
          key={source}
          style={[
            {
              flex: 1,
              marginHorizontal: 4,
              backgroundColor: selectedSource === source ? colors.accent : colors.surface,
            },
          ]}
          onPress={() => onSourceChange(source)}
        >
          <View style={{ padding: 12, alignItems: 'center' }}>
            <Text
              style={[
                {
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: selectedSource === source ? colors.accentText : colors.text,
                },
              ]}
            >
              {source}
            </Text>
          </View>
        </Card>
      ))}
    </View>
  );
};

export default RecipeSourceTabs;
