import { useState, useEffect, useCallback } from 'react';
import { auth } from '@/firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import {
  Recipe,
  InventoryItem,
  DietPreference,
  MealType,
  RecipeSource,
  FilterMealType,
  InstructionType,
  numRecipesPerMealTypeInitialState
} from '@/components/types';
import { generateRecipeBasicsAsync, generateRecipeDetailsAsync } from '@/services/generateRecipes';
import { RecipeService } from '@/services/RecipeService';
import { MAX_RECIPES_PER_MEAL_TYPE } from '@/constants/RecipeConstants';

export interface UseRecipeManagerReturn {
  // State
  forYouRecipes: Recipe[];
  fromInventoryRecipes: Recipe[];
  isLoading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  error: string | null;
  selectedSource: RecipeSource;
  selectedMealType: FilterMealType;
  expandedRecipeIds: Set<string>;
  loadingRecipeDetails: string | null;
  loadedDetailRecipeIds: Set<string>;
  lastResponseId: string | null;
  recipesPerMealType: { [key in MealType]: number };
  
  // Actions
  setSelectedSource: (source: RecipeSource) => void;
  setSelectedMealType: (mealType: FilterMealType) => void;
  setExpandedRecipeIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  getRecipes: () => Promise<void>;
  loadMoreRecipes: () => Promise<void>;
  onRefresh: () => void;
  toggleExpanded: (recipeId: string) => Promise<void>;
  
  // Computed
  currentRecipes: Recipe[];
  filteredRecipes: Recipe[];
  canLoadMore: boolean;
}

export const useRecipeManager = (): UseRecipeManagerReturn => {
  // State
  const [forYouRecipes, setForYouRecipes] = useState<Recipe[]>([]);
  const [fromInventoryRecipes, setFromInventoryRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSource, setSelectedSource] = useState<RecipeSource>(RecipeSource.FOR_YOU);
  const [selectedMealType, setSelectedMealType] = useState<FilterMealType>('All');
  const [expandedRecipeIds, setExpandedRecipeIds] = useState<Set<string>>(new Set());
  const [loadingRecipeDetails, setLoadingRecipeDetails] = useState<string | null>(null);
  const [loadedDetailRecipeIds, setLoadedDetailRecipeIds] = useState<Set<string>>(new Set());
  const [lastResponseId, setLastResponseId] = useState<string | null>(null);
  const [recipesPerMealType, setRecipesPerMealType] = useState<{ [key in MealType]: number }>(
    numRecipesPerMealTypeInitialState
  );

  // Fetch pre-generated recipes from Firestore (For You tab)
  const getFirestoreRecipes = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      console.log('Fetching Firestore recipes...');
      const firestoreRecipes = await RecipeService.fetchRecipesFromFirestore(user.uid);

      if (firestoreRecipes.length > 0) {
        console.log(`Firestore recipes loaded:`, firestoreRecipes.length, 'recipes');
        setForYouRecipes(firestoreRecipes);
        setIsLoading(false);

        const firestoreRecipeIds = new Set(firestoreRecipes.map((r) => r.id));
        setLoadedDetailRecipeIds(firestoreRecipeIds);
      }
    } catch (error) {
      console.error('Error fetching Firestore recipes:', error);
    }
  };

  // Fetch recipes from LLM based on inventory (From Inventory tab)
  const getLLMRecipes = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      // Fetch inventory document
      const inventoryDoc = await firestoreRepository.getDocument(FirestoreCollections.INVENTORY, user.uid);
      const inventoryItems = (inventoryDoc?.items || []) as InventoryItem[];

      // Transform to ingredients
      const ingredients = inventoryItems
        .filter((item) => item.name && item.quantity > 0)
        .map((item) => ({
          name: item.name,
          available: true,
        }));

      // Fetch diet preferences
      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      console.log('Fetching LLM recipes...');
      const { recipes: llmRecipes, responseId } = await generateRecipeBasicsAsync(
        ingredients,
        dietPrefsDoc,
        numRecipesPerMealTypeInitialState,
        fromInventoryRecipes.map((r) => r.title)
      );

      if (llmRecipes.length > 0) {
        console.log(`LLM recipes loaded:`, llmRecipes.length, 'recipes');
        setFromInventoryRecipes(llmRecipes);
        setLastResponseId(responseId);
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching LLM recipes:', error);
    }
  };

  // Main function that gets recipes based on selected source
  const getRecipes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Reset recipes per meal type
      setRecipesPerMealType(numRecipesPerMealTypeInitialState);
      setLoadedDetailRecipeIds(new Set());

      // Clear existing recipes for both sources
      setForYouRecipes([]);
      setFromInventoryRecipes([]);

      // Start both calls independently and simultaneously
      getFirestoreRecipes();
      getLLMRecipes();
    } catch (error) {
      console.error('Error updating recipes:', error);
      setError('Failed to update recipes');
      setIsLoading(false);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreRecipes = async () => {
    try {
      // Only load more for "From Inventory" tab since "For You" tab loads from Firestore
      if (selectedSource !== RecipeSource.FROM_INVENTORY) return;

      // Don't load more if already at max recipes for the current meal type
      if (selectedMealType !== 'All' && recipesPerMealType[selectedMealType as MealType] >= MAX_RECIPES_PER_MEAL_TYPE) {
        return;
      }

      // Don't load more if all meal types have reached their max
      if (
        selectedMealType === 'All' &&
        Object.values(MealType).every((type) => recipesPerMealType[type] >= MAX_RECIPES_PER_MEAL_TYPE)
      ) {
        return;
      }

      setLoadingMore(true);
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');

      // Fetch inventory and diet preferences
      const inventoryDoc = await firestoreRepository.getDocument(FirestoreCollections.INVENTORY, user.uid);
      const inventoryItems = (inventoryDoc?.items || []) as InventoryItem[];
      const ingredients = inventoryItems
        .filter((item) => item.name && item.quantity > 0)
        .map((item) => ({ name: item.name, available: true }));

      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      // Generate more recipes logic (simplified for brevity)
      if (ingredients.length > 0) {
        const updatedCounts = { ...recipesPerMealType };
        
        if (selectedMealType === 'All') {
          Object.values(MealType).forEach((mealType) => {
            if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
              updatedCounts[mealType]++;
            }
          });
        } else {
          const mealType = selectedMealType as MealType;
          if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
            updatedCounts[mealType]++;
          }
        }

        setRecipesPerMealType(updatedCounts);

        // Generate new recipes based on meal type selection
        const { recipes: newRecipes, responseId } = await generateRecipeBasicsAsync(
          ingredients,
          dietPrefsDoc,
          selectedMealType === 'All' ? numRecipesPerMealTypeInitialState : { [selectedMealType]: 1 },
          fromInventoryRecipes.map((r) => r.title)
        );

        setLastResponseId(responseId);

        // Combine with existing recipes
        setFromInventoryRecipes((prevRecipes) => {
          const combinedRecipes = [...prevRecipes];
          newRecipes.forEach((newRecipe) => {
            if (!combinedRecipes.some((r) => r.id === newRecipe.id)) {
              combinedRecipes.push(newRecipe);
            }
          });
          return combinedRecipes;
        });
      }
    } catch (error) {
      console.error('Error loading more recipes:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getRecipes();
  }, []);

  const toggleExpanded = async (recipeId: string) => {
    // If we're already expanded, just collapse
    if (expandedRecipeIds.has(recipeId)) {
      setExpandedRecipeIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.delete(recipeId);
        return newIds;
      });
      return;
    }

    // First, expand the recipe immediately to show the loading skeleton
    setExpandedRecipeIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.add(recipeId);
      return newIds;
    });

    // Check if we need to fetch details
    const currentRecipes = selectedSource === RecipeSource.FOR_YOU ? forYouRecipes : fromInventoryRecipes;
    const recipe = currentRecipes.find((r) => r.id === recipeId);
    const hasCompleteDetails =
      recipe &&
      recipe.ingredients.length > 0 &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available';

    // If we need to fetch details, do it after expanding
    if (!loadedDetailRecipeIds.has(recipeId) && !hasCompleteDetails && lastResponseId) {
      setLoadingRecipeDetails(recipeId);

      try {
        const user = auth.currentUser;
        if (!user || !recipe) throw new Error('User not authenticated or recipe not found');

        const details = await generateRecipeDetailsAsync(recipe.id, recipe.title, recipe.mealType, lastResponseId);

        // Update the recipe with the details
        if (selectedSource === RecipeSource.FOR_YOU) {
          setForYouRecipes((prevRecipes) =>
            prevRecipes.map((r) =>
              r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
            )
          );
        } else {
          setFromInventoryRecipes((prevRecipes) =>
            prevRecipes.map((r) =>
              r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
            )
          );
        }

        setLoadedDetailRecipeIds((prev) => {
          const newSet = new Set(prev);
          newSet.add(recipeId);
          return newSet;
        });
      } catch (error) {
        console.error('Error loading recipe details:', error);
      } finally {
        setLoadingRecipeDetails(null);
      }
    } else if (hasCompleteDetails) {
      setLoadedDetailRecipeIds((prev) => {
        const newSet = new Set(prev);
        newSet.add(recipeId);
        return newSet;
      });
    }
  };

  // Initial fetch
  useEffect(() => {
    getRecipes();
  }, []);

  // Handle tab switching
  useEffect(() => {
    setSelectedMealType('All');
  }, [selectedSource]);

  // Computed values
  const currentRecipes = selectedSource === RecipeSource.FOR_YOU ? forYouRecipes : fromInventoryRecipes;
  
  const filteredRecipes = currentRecipes.filter((recipe) => {
    if (selectedMealType !== 'All' && recipe.mealType !== selectedMealType) {
      return false;
    }
    return true;
  });

  const canLoadMore =
    selectedSource === RecipeSource.FROM_INVENTORY &&
    (selectedMealType === 'All'
      ? Object.values(MealType).some((type) => recipesPerMealType[type] < MAX_RECIPES_PER_MEAL_TYPE)
      : recipesPerMealType[selectedMealType as MealType] < MAX_RECIPES_PER_MEAL_TYPE);

  return {
    // State
    forYouRecipes,
    fromInventoryRecipes,
    isLoading,
    refreshing,
    loadingMore,
    error,
    selectedSource,
    selectedMealType,
    expandedRecipeIds,
    loadingRecipeDetails,
    loadedDetailRecipeIds,
    lastResponseId,
    recipesPerMealType,
    
    // Actions
    setSelectedSource,
    setSelectedMealType,
    setExpandedRecipeIds,
    getRecipes,
    loadMoreRecipes,
    onRefresh,
    toggleExpanded,
    
    // Computed
    currentRecipes,
    filteredRecipes,
    canLoadMore,
  };
};
