import { useState } from 'react';

export interface UseFavoritesReturn {
  favorites: Set<string>;
  toggleFavorite: (recipeId: string, e: any) => void;
}

export const useFavorites = (): UseFavoritesReturn => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  const toggleFavorite = (recipeId: string, e: any) => {
    e.stopPropagation();
    const newFavorites = new Set(favorites);
    if (favorites.has(recipeId)) {
      newFavorites.delete(recipeId);
    } else {
      newFavorites.add(recipeId);
    }
    setFavorites(newFavorites);
  };

  return {
    favorites,
    toggleFavorite,
  };
};
